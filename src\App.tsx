import React, { useState, useEffect } from 'react';
import { Printer, Download, Eye, Plus, Trash2, Store, CreditCard, Hash, QrCode, Settings } from 'lucide-react';
import QRCodeComponent from 'qrcode.react';

interface BusinessInfo {
  name: string;
  address: string;
  contact: string;
  logo?: string;
}

interface ReceiptItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

interface Receipt {
  id?: string;
  businessInfo: BusinessInfo;
  items: ReceiptItem[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: string;
  transactionRef: string;
  customization: {
    fontSize: number;
    showLogo: boolean;
    footerText: string;
    paperWidth: '80mm' | '50mm';
  };
}

const API_BASE = 'http://localhost:3001/api';

// Thermal paper specifications
const PAPER_SPECS = {
  '80mm': {
    width: 302, // ~80mm in pixels at 96 DPI
    contentWidth: 270, // Content area with margins
    margin: 16,
    fontSize: {
      small: 11,
      normal: 12,
      large: 14,
      xlarge: 16
    },
    qrSize: 100,
    lineHeight: 1.3
  },
  '50mm': {
    width: 189, // ~50mm in pixels at 96 DPI
    contentWidth: 165, // Content area with margins
    margin: 12,
    fontSize: {
      small: 9,
      normal: 10,
      large: 12,
      xlarge: 14
    },
    qrSize: 70,
    lineHeight: 1.2
  }
};

function App() {
  const [receipt, setReceipt] = useState<Receipt>({
    businessInfo: {
      name: 'My Coffee Shop',
      address: '123 Main St, City, State 12345',
      contact: 'Phone: (*************',
    },
    items: [
      { id: '1', name: 'Coffee', quantity: 2, price: 4.50 },
      { id: '2', name: 'Sandwich', quantity: 1, price: 8.99 },
    ],
    subtotal: 0,
    tax: 0,
    total: 0,
    paymentMethod: 'Credit Card',
    transactionRef: 'TXN-' + Date.now().toString(36).toUpperCase(),
    customization: {
      fontSize: 12,
      showLogo: true,
      footerText: 'Thank you for your business!',
      paperWidth: '80mm',
    },
  });

  const [qrCode, setQrCode] = useState('');
  const [isPrinting, setIsPrinting] = useState(false);
  const [savedReceipts, setSavedReceipts] = useState<Receipt[]>([]);

  const currentSpecs = PAPER_SPECS[receipt.customization.paperWidth];

  // Calculate totals
  useEffect(() => {
    const subtotal = receipt.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + tax;

    setReceipt(prev => ({
      ...prev,
      subtotal,
      tax,
      total,
    }));
  }, [receipt.items]);

  // Generate QR code
  useEffect(() => {
    const generateQR = async () => {
      try {
        const qrData = JSON.stringify({
          business: receipt.businessInfo.name,
          total: receipt.total,
          transaction: receipt.transactionRef,
          timestamp: new Date().toISOString(),
        });

        const response = await fetch(`${API_BASE}/qr-code`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ data: qrData }),
        });

        const result = await response.json();
        setQrCode(result.qrCode);
      } catch (error) {
        console.error('Failed to generate QR code:', error);
      }
    };

    generateQR();
  }, [receipt.businessInfo.name, receipt.total, receipt.transactionRef]);

  // Add item
  const addItem = () => {
    const newItem: ReceiptItem = {
      id: Date.now().toString(),
      name: 'New Item',
      quantity: 1,
      price: 0,
    };
    setReceipt(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  // Remove item
  const removeItem = (id: string) => {
    setReceipt(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  // Update item
  const updateItem = (id: string, field: keyof ReceiptItem, value: any) => {
    setReceipt(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      ),
    }));
  };

  // Save receipt
  const saveReceipt = async () => {
    try {
      const response = await fetch(`${API_BASE}/receipts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(receipt),
      });

      const savedReceipt = await response.json();
      setReceipt(savedReceipt);
      loadReceipts();
    } catch (error) {
      console.error('Failed to save receipt:', error);
    }
  };

  // Load receipts
  const loadReceipts = async () => {
    try {
      const response = await fetch(`${API_BASE}/receipts`);
      const receipts = await response.json();
      setSavedReceipts(receipts);
    } catch (error) {
      console.error('Failed to load receipts:', error);
    }
  };

  // Print receipt
  const printReceipt = async () => {
    if (!receipt.id) {
      await saveReceipt();
    }

    setIsPrinting(true);
    try {
      const response = await fetch(`${API_BASE}/print/${receipt.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ paperWidth: receipt.customization.paperWidth }),
      });

      const result = await response.json();
      if (result.success) {
        alert('Receipt printed successfully!');
      }
    } catch (error) {
      console.error('Failed to print receipt:', error);
      alert('Failed to print receipt');
    } finally {
      setIsPrinting(false);
    }
  };

  // Export PDF
  const exportPDF = async () => {
    if (!receipt.id) {
      await saveReceipt();
    }

    try {
      const response = await fetch(`${API_BASE}/pdf/${receipt.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ paperWidth: receipt.customization.paperWidth }),
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `receipt-${receipt.id}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export PDF:', error);
    }
  };

  useEffect(() => {
    loadReceipts();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Store className="w-8 h-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Thermal Receipt Generator</h1>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={saveReceipt}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Save Receipt
              </button>
              <button
                onClick={exportPDF}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export PDF</span>
              </button>
              <button
                onClick={printReceipt}
                disabled={isPrinting}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
              >
                <Printer className="w-4 h-4" />
                <span>{isPrinting ? 'Printing...' : 'Print'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Receipt Designer */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6 flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>Receipt Designer</span>
            </h2>

            {/* Paper Size Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Paper Size</h3>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paperWidth"
                    value="80mm"
                    checked={receipt.customization.paperWidth === '80mm'}
                    onChange={(e) => setReceipt(prev => ({
                      ...prev,
                      customization: { ...prev.customization, paperWidth: e.target.value as '80mm' | '50mm' }
                    }))}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium">80mm (Standard)</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paperWidth"
                    value="50mm"
                    checked={receipt.customization.paperWidth === '50mm'}
                    onChange={(e) => setReceipt(prev => ({
                      ...prev,
                      customization: { ...prev.customization, paperWidth: e.target.value as '80mm' | '50mm' }
                    }))}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium">50mm (Compact)</span>
                </label>
              </div>
              <div className="mt-2 text-xs text-gray-500">
                Current: {currentSpecs.width}px width, {currentSpecs.contentWidth}px content area
              </div>
            </div>

            {/* Business Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Business Information</h3>
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Business Name"
                  value={receipt.businessInfo.name}
                  onChange={(e) => setReceipt(prev => ({
                    ...prev,
                    businessInfo: { ...prev.businessInfo, name: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <textarea
                  placeholder="Address (use line breaks for multiple lines)"
                  value={receipt.businessInfo.address}
                  onChange={(e) => setReceipt(prev => ({
                    ...prev,
                    businessInfo: { ...prev.businessInfo, address: e.target.value }
                  }))}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="text"
                  placeholder="Contact Information"
                  value={receipt.businessInfo.contact}
                  onChange={(e) => setReceipt(prev => ({
                    ...prev,
                    businessInfo: { ...prev.businessInfo, contact: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Items */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium">Items</h3>
                <button
                  onClick={addItem}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Item</span>
                </button>
              </div>
              <div className="space-y-3">
                {receipt.items.map((item) => (
                  <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                    <input
                      type="text"
                      placeholder="Item name"
                      value={item.name}
                      onChange={(e) => updateItem(item.id, 'name', e.target.value)}
                      className="col-span-5 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    />
                    <input
                      type="number"
                      placeholder="Qty"
                      value={item.quantity}
                      onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                      className="col-span-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    />
                    <input
                      type="number"
                      step="0.01"
                      placeholder="Price"
                      value={item.price}
                      onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                      className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    />
                    <div className="col-span-1 text-center text-sm font-medium">
                      ${(item.quantity * item.price).toFixed(2)}
                    </div>
                    <button
                      onClick={() => removeItem(item.id)}
                      className="col-span-1 p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Payment Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium mb-1">Payment Method</label>
                  <select
                    value={receipt.paymentMethod}
                    onChange={(e) => setReceipt(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Credit Card">Credit Card</option>
                    <option value="Debit Card">Debit Card</option>
                    <option value="Cash">Cash</option>
                    <option value="Mobile Payment">Mobile Payment</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Transaction Reference</label>
                  <input
                    type="text"
                    value={receipt.transactionRef}
                    onChange={(e) => setReceipt(prev => ({ ...prev, transactionRef: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Customization */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Customization</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">Footer Text</label>
                  <input
                    type="text"
                    value={receipt.customization.footerText}
                    onChange={(e) => setReceipt(prev => ({
                      ...prev,
                      customization: { ...prev.customization, footerText: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Receipt Preview */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6 flex items-center space-x-2">
              <Eye className="w-5 h-5" />
              <span>Receipt Preview ({receipt.customization.paperWidth})</span>
            </h2>

            {/* Thermal Receipt */}
            <div className="flex justify-center">
              <div 
                className="bg-white border-2 border-gray-300 shadow-lg font-mono"
                style={{ 
                  width: `${currentSpecs.width}px`,
                  minHeight: '400px',
                  padding: `${currentSpecs.margin}px`,
                  fontSize: `${currentSpecs.fontSize.normal}px`,
                  lineHeight: currentSpecs.lineHeight,
                  fontFamily: 'Courier New, monospace'
                }}
              >
                {/* Business Header */}
                <div className="text-center mb-4" style={{ marginBottom: `${currentSpecs.margin}px` }}>
                  <div 
                    className="font-bold mb-1" 
                    style={{ 
                      fontSize: `${currentSpecs.fontSize.large}px`,
                      marginBottom: `${currentSpecs.margin / 4}px`,
                      wordWrap: 'break-word'
                    }}
                  >
                    {receipt.businessInfo.name}
                  </div>
                  <div 
                    className="text-sm mb-1" 
                    style={{ 
                      fontSize: `${currentSpecs.fontSize.small}px`,
                      marginBottom: `${currentSpecs.margin / 4}px`,
                      whiteSpace: 'pre-line',
                      wordWrap: 'break-word'
                    }}
                  >
                    {receipt.businessInfo.address}
                  </div>
                  <div 
                    className="text-sm" 
                    style={{ 
                      fontSize: `${currentSpecs.fontSize.small}px`,
                      wordWrap: 'break-word'
                    }}
                  >
                    {receipt.businessInfo.contact}
                  </div>
                </div>

                {/* Separator */}
                <div 
                  className="border-t border-gray-400" 
                  style={{ 
                    margin: `${currentSpecs.margin / 2}px 0`,
                    borderTop: '1px dashed #666'
                  }}
                ></div>

                {/* Items */}
                <div className="mb-4" style={{ marginBottom: `${currentSpecs.margin}px` }}>
                  {receipt.items.map((item) => (
                    <div key={item.id} style={{ marginBottom: `${currentSpecs.margin / 2}px` }}>
                      <div className="flex justify-between items-start">
                        <div className="flex-1" style={{ paddingRight: `${currentSpecs.margin / 2}px` }}>
                          <div 
                            style={{ 
                              fontSize: `${currentSpecs.fontSize.normal}px`,
                              wordWrap: 'break-word',
                              lineHeight: currentSpecs.lineHeight
                            }}
                          >
                            {item.name}
                          </div>
                          <div 
                            className="text-gray-600" 
                            style={{ 
                              fontSize: `${currentSpecs.fontSize.small}px`,
                              marginTop: `${currentSpecs.margin / 8}px`
                            }}
                          >
                            {item.quantity} x ${item.price.toFixed(2)}
                          </div>
                        </div>
                        <div 
                          className="font-medium text-right" 
                          style={{ 
                            fontSize: `${currentSpecs.fontSize.normal}px`,
                            minWidth: '60px'
                          }}
                        >
                          ${(item.price * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Separator */}
                <div 
                  className="border-t border-gray-400" 
                  style={{ 
                    margin: `${currentSpecs.margin / 2}px 0`,
                    borderTop: '1px dashed #666'
                  }}
                ></div>

                {/* Totals */}
                <div className="mb-4" style={{ marginBottom: `${currentSpecs.margin}px` }}>
                  <div className="flex justify-between" style={{ marginBottom: `${currentSpecs.margin / 4}px` }}>
                    <span style={{ fontSize: `${currentSpecs.fontSize.normal}px` }}>Subtotal:</span>
                    <span style={{ fontSize: `${currentSpecs.fontSize.normal}px` }}>${receipt.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between" style={{ marginBottom: `${currentSpecs.margin / 4}px` }}>
                    <span style={{ fontSize: `${currentSpecs.fontSize.normal}px` }}>Tax (8%):</span>
                    <span style={{ fontSize: `${currentSpecs.fontSize.normal}px` }}>${receipt.tax.toFixed(2)}</span>
                  </div>
                  <div 
                    className="flex justify-between font-bold border-t pt-1" 
                    style={{ 
                      fontSize: `${currentSpecs.fontSize.large}px`,
                      paddingTop: `${currentSpecs.margin / 4}px`,
                      borderTop: '1px solid #333'
                    }}
                  >
                    <span>TOTAL:</span>
                    <span>${receipt.total.toFixed(2)}</span>
                  </div>
                </div>

                {/* Separator */}
                <div 
                  className="border-t border-gray-400" 
                  style={{ 
                    margin: `${currentSpecs.margin / 2}px 0`,
                    borderTop: '1px dashed #666'
                  }}
                ></div>

                {/* Payment Info */}
                <div className="mb-4" style={{ marginBottom: `${currentSpecs.margin}px` }}>
                  <div className="flex justify-between" style={{ marginBottom: `${currentSpecs.margin / 4}px` }}>
                    <span style={{ fontSize: `${currentSpecs.fontSize.small}px` }}>Payment:</span>
                    <span style={{ fontSize: `${currentSpecs.fontSize.small}px` }}>{receipt.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span style={{ fontSize: `${currentSpecs.fontSize.small}px` }}>Ref:</span>
                    <span 
                      style={{ 
                        fontSize: `${currentSpecs.fontSize.small}px`,
                        wordBreak: 'break-all'
                      }}
                    >
                      {receipt.transactionRef}
                    </span>
                  </div>
                </div>

                {/* QR Code */}
                <div className="flex justify-center mb-4" style={{ marginBottom: `${currentSpecs.margin}px` }}>
                  <QRCodeComponent
                    value={JSON.stringify({
                      business: receipt.businessInfo.name,
                      total: receipt.total,
                      transaction: receipt.transactionRef,
                      timestamp: new Date().toISOString(),
                    })}
                    size={currentSpecs.qrSize}
                    level="M"
                    includeMargin={false}
                  />
                </div>

                {/* Footer */}
                <div 
                  className="text-center" 
                  style={{ 
                    fontSize: `${currentSpecs.fontSize.small}px`,
                    marginBottom: `${currentSpecs.margin / 2}px`,
                    wordWrap: 'break-word'
                  }}
                >
                  {receipt.customization.footerText}
                </div>
                
                <div 
                  className="text-center text-gray-500" 
                  style={{ 
                    fontSize: `${currentSpecs.fontSize.small - 1}px`,
                    wordWrap: 'break-word'
                  }}
                >
                  {new Date().toLocaleString()}
                </div>
              </div>
            </div>

            {/* Totals Summary */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">Paper Size:</span>
                <span className="font-medium">{receipt.customization.paperWidth}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">Items:</span>
                <span className="font-medium">{receipt.items.length}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">Subtotal:</span>
                <span className="font-medium">${receipt.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm">Tax (8%):</span>
                <span className="font-medium">${receipt.tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total:</span>
                <span className="text-blue-600">${receipt.total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Saved Receipts */}
        {savedReceipts.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Saved Receipts</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {savedReceipts.map((savedReceipt) => (
                <div key={savedReceipt.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium truncate">{savedReceipt.businessInfo.name}</h3>
                    <span className="text-sm text-gray-500">${savedReceipt.total.toFixed(2)}</span>
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    {savedReceipt.items.length} items • {savedReceipt.paymentMethod} • {savedReceipt.customization.paperWidth}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setReceipt(savedReceipt)}
                      className="flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                    >
                      Load
                    </button>
                    <button
                      onClick={() => printReceipt()}
                      className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors"
                    >
                      <Printer className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;