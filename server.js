import express from 'express';
import cors from 'cors';
import QRCode from 'qrcode';
import { ThermalPrinter } from 'node-thermal-printer';
import PDFDocument from 'pdfkit';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage for receipts (in production, use a database)
const receipts = [];

// Initialize thermal printer (simulate if no printer available)
let printer;
try {
  printer = new ThermalPrinter({
    type: 'epson',
    interface: 'printer:Auto',
    options: {
      timeout: 5000,
    },
  });
} catch (error) {
  console.log('Thermal printer not available, using simulation mode');
  printer = null;
}

// API Routes

// Generate QR Code
app.post('/api/qr-code', async (req, res) => {
  try {
    const { data } = req.body;
    const qrCode = await QRCode.toDataURL(data);
    res.json({ qrCode });
  } catch (error) {
    res.status(500).json({ error: 'Failed to generate QR code' });
  }
});

// Save receipt
app.post('/api/receipts', (req, res) => {
  try {
    const receipt = {
      id: uuidv4(),
      ...req.body,
      timestamp: new Date().toISOString(),
    };
    receipts.push(receipt);
    res.json(receipt);
  } catch (error) {
    res.status(500).json({ error: 'Failed to save receipt' });
  }
});

// Get all receipts
app.get('/api/receipts', (req, res) => {
  res.json(receipts);
});

// Get receipt by ID
app.get('/api/receipts/:id', (req, res) => {
  const receipt = receipts.find(r => r.id === req.params.id);
  if (!receipt) {
    return res.status(404).json({ error: 'Receipt not found' });
  }
  res.json(receipt);
});

// Print receipt
app.post('/api/print/:id', async (req, res) => {
  try {
    const receipt = receipts.find(r => r.id === req.params.id);
    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    const { paperWidth = '80mm' } = req.body;
    const charWidth = paperWidth === '80mm' ? 48 : 32; // Characters per line

    if (printer) {
      // Actual thermal printer code
      printer.setCharacterSet('PC437_USA');
      printer.setTextSize(0, 0); // Normal size
      printer.alignCenter();
      printer.bold(true);
      printer.println(receipt.businessInfo.name);
      printer.bold(false);
      
      // Handle multi-line address
      const addressLines = receipt.businessInfo.address.split('\n');
      addressLines.forEach(line => {
        if (line.trim()) printer.println(line.trim());
      });
      
      printer.println(receipt.businessInfo.contact);
      
      // Print dashed line
      printer.println('-'.repeat(charWidth));
      
      printer.alignLeft();
      receipt.items.forEach(item => {
        const itemName = item.name.length > charWidth - 10 ? 
          item.name.substring(0, charWidth - 13) + '...' : item.name;
        const itemTotal = `$${(item.price * item.quantity).toFixed(2)}`;
        const spaces = charWidth - itemName.length - itemTotal.length;
        
        printer.println(`${itemName}${' '.repeat(Math.max(1, spaces))}${itemTotal}`);
        printer.println(`  ${item.quantity} x $${item.price.toFixed(2)}`);
      });
      
      printer.println('-'.repeat(charWidth));
      printer.alignRight();
      printer.println(`Subtotal: $${receipt.subtotal.toFixed(2)}`);
      printer.println(`Tax (8%): $${receipt.tax.toFixed(2)}`);
      printer.println('-'.repeat(charWidth));
      printer.bold(true);
      printer.println(`TOTAL: $${receipt.total.toFixed(2)}`);
      printer.bold(false);
      printer.println('-'.repeat(charWidth));
      printer.alignLeft();
      printer.println(`Payment: ${receipt.paymentMethod}`);
      printer.println(`Ref: ${receipt.transactionRef}`);
      printer.newLine();
      
      // Generate QR code for printing
      const qrData = JSON.stringify({
        id: receipt.id,
        total: receipt.total,
        date: receipt.timestamp
      });
      const qrCode = await QRCode.toString(qrData, { type: 'terminal', width: paperWidth === '80mm' ? 25 : 20 });
      printer.println(qrCode);
      
      await printer.execute();
      res.json({ success: true, message: 'Receipt printed successfully' });
    } else {
      // Simulate printing
      console.log(`=== THERMAL RECEIPT ${paperWidth.toUpperCase()} (SIMULATED) ===`);
      console.log(receipt.businessInfo.name);
      const addressLines = receipt.businessInfo.address.split('\n');
      addressLines.forEach(line => {
        if (line.trim()) console.log(line.trim());
      });
      console.log(receipt.businessInfo.contact);
      console.log('-'.repeat(charWidth));
      receipt.items.forEach(item => {
        console.log(`${item.name.padEnd(charWidth - 10)}$${(item.price * item.quantity).toFixed(2).padStart(8)}`);
        console.log(`  ${item.quantity} x $${item.price.toFixed(2)}`);
      });
      console.log('-'.repeat(charWidth));
      console.log(`${'Subtotal:'.padEnd(charWidth - 10)}$${receipt.subtotal.toFixed(2).padStart(8)}`);
      console.log(`${'Tax (8%):'.padEnd(charWidth - 10)}$${receipt.tax.toFixed(2).padStart(8)}`);
      console.log('-'.repeat(charWidth));
      console.log(`Total: $${receipt.total.toFixed(2)}`);
      console.log(`Payment: ${receipt.paymentMethod}`);
      console.log(`Transaction: ${receipt.transactionRef}`);
      console.log('================================');
      
      res.json({ success: true, message: 'Receipt printed successfully (simulated)' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Failed to print receipt' });
  }
});

// Generate PDF
app.post('/api/pdf/:id', async (req, res) => {
  try {
    const receipt = receipts.find(r => r.id === req.params.id);
    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    const { paperWidth = '80mm' } = req.body;
    
    // Paper specifications for PDF generation
    const pdfSpecs = {
      '80mm': { width: 226, height: 600, margin: 10, fontSize: { normal: 10, small: 8, large: 12 } },
      '50mm': { width: 142, height: 600, margin: 8, fontSize: { normal: 8, small: 7, large: 10 } }
    };
    
    const specs = pdfSpecs[paperWidth];
    const doc = new PDFDocument({ size: [specs.width, specs.height], margin: specs.margin });
    const filename = `receipt-${receipt.id}.pdf`;
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    
    doc.pipe(res);
    
    // Business header
    doc.fontSize(specs.fontSize.large).text(receipt.businessInfo.name, { align: 'center' });
    doc.fontSize(specs.fontSize.small).text(receipt.businessInfo.address, { align: 'center' });
    doc.text(receipt.businessInfo.contact, { align: 'center' });
    doc.moveDown();
    
    // Dashed line separator
    const y1 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y1).lineTo(x + 2, y1).stroke();
    }
    doc.moveDown(0.5);
    
    // Items
    doc.fontSize(specs.fontSize.normal);
    receipt.items.forEach(item => {
      const itemText = `${item.name}`;
      const priceText = `$${(item.price * item.quantity).toFixed(2)}`;
      const qtyText = `${item.quantity} x $${item.price.toFixed(2)}`;
      
      doc.text(itemText, { continued: false });
      doc.fontSize(specs.fontSize.small).text(qtyText, { indent: 10 });
      doc.fontSize(specs.fontSize.normal);
      doc.text(priceText, doc.x, doc.y - 20, { align: 'right' });
    });
    
    doc.moveDown();
    
    // Dashed line separator
    const y2 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y2).lineTo(x + 2, y2).stroke();
    }
    doc.moveDown(0.5);
    
    doc.fontSize(specs.fontSize.normal).text(`Subtotal: $${receipt.subtotal.toFixed(2)}`, { align: 'right' });
    doc.text(`Tax: $${receipt.tax.toFixed(2)}`, { align: 'right' });
    doc.fontSize(specs.fontSize.large).text(`TOTAL: $${receipt.total.toFixed(2)}`, { align: 'right' });
    doc.fontSize(specs.fontSize.normal);
    doc.moveDown();
    
    // Dashed line separator
    const y3 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y3).lineTo(x + 2, y3).stroke();
    }
    doc.moveDown(0.5);
    
    doc.fontSize(specs.fontSize.small).text(`Payment: ${receipt.paymentMethod}`);
    doc.text(`Ref: ${receipt.transactionRef}`);
    
    // QR Code
    const qrData = JSON.stringify({
      id: receipt.id,
      total: receipt.total,
      date: receipt.timestamp
    });
    const qrCode = await QRCode.toDataURL(qrData);
    const qrBuffer = Buffer.from(qrCode.split(',')[1], 'base64');
    const qrSize = paperWidth === '80mm' ? 80 : 60;
    doc.image(qrBuffer, { fit: [qrSize, qrSize], align: 'center' });
    
    doc.end();
  } catch (error) {
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});