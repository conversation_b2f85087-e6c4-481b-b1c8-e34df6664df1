import express from 'express';
import cors from 'cors';
import QRCode from 'qrcode';
import { ThermalPrinter } from 'node-thermal-printer';
import PDFDocument from 'pdfkit';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage for receipts (in production, use a database)
const receipts = [];

// Initialize thermal printer with better default printer detection
let printer;

// Function to get available printers and select default
async function initializePrinter() {
  try {
    // Try to initialize with automatic printer detection
    printer = new ThermalPrinter({
      type: 'epson', // Most common thermal printer type
      interface: 'printer:Auto', // Auto-detect printer
      options: {
        timeout: 10000, // Increased timeout for better reliability
      },
    });

    // Test printer connection
    const isConnected = await printer.isPrinterConnected();
    if (isConnected) {
      console.log('✅ Thermal printer connected successfully');
      return true;
    } else {
      console.log('⚠️ Thermal printer not responding, trying alternative methods...');

      // Try with different interfaces
      const interfaces = [
        'printer:', // Default system printer
        'usb:', // USB thermal printers
        'serial:', // Serial thermal printers
      ];

      for (const iface of interfaces) {
        try {
          printer = new ThermalPrinter({
            type: 'epson',
            interface: iface,
            options: {
              timeout: 5000,
            },
          });

          const connected = await printer.isPrinterConnected();
          if (connected) {
            console.log(`✅ Printer connected via ${iface}`);
            return true;
          }
        } catch (err) {
          console.log(`❌ Failed to connect via ${iface}: ${err.message}`);
        }
      }
    }
  } catch (error) {
    console.log('❌ Thermal printer initialization failed:', error.message);
  }

  // Fallback to system default printer using different approach
  try {
    printer = new ThermalPrinter({
      type: 'epson',
      interface: 'printer:', // Use system default printer
      options: {
        timeout: 5000,
      },
    });
    console.log('🔄 Using system default printer as fallback');
    return true;
  } catch (error) {
    console.log('❌ All printer initialization methods failed, using simulation mode');
    printer = null;
    return false;
  }
}

// Initialize printer on startup
initializePrinter();

// API Routes

// Generate QR Code
app.post('/api/qr-code', async (req, res) => {
  try {
    const { data } = req.body;
    const qrCode = await QRCode.toDataURL(data);
    res.json({ qrCode });
  } catch (error) {
    res.status(500).json({ error: 'Failed to generate QR code' });
  }
});

// Save receipt
app.post('/api/receipts', (req, res) => {
  try {
    const receipt = {
      id: uuidv4(),
      ...req.body,
      timestamp: new Date().toISOString(),
    };
    receipts.push(receipt);
    res.json(receipt);
  } catch (error) {
    res.status(500).json({ error: 'Failed to save receipt' });
  }
});

// Get all receipts
app.get('/api/receipts', (req, res) => {
  res.json(receipts);
});

// Get receipt by ID
app.get('/api/receipts/:id', (req, res) => {
  const receipt = receipts.find(r => r.id === req.params.id);
  if (!receipt) {
    return res.status(404).json({ error: 'Receipt not found' });
  }
  res.json(receipt);
});

// Get printer status
app.get('/api/printer/status', async (req, res) => {
  try {
    let status = {
      connected: false,
      type: 'none',
      message: 'No printer detected'
    };

    if (printer) {
      try {
        const isConnected = await printer.isPrinterConnected();
        status = {
          connected: isConnected,
          type: isConnected ? 'thermal' : 'disconnected',
          message: isConnected ? 'Thermal printer ready' : 'Thermal printer not responding'
        };
      } catch (error) {
        status = {
          connected: false,
          type: 'error',
          message: `Printer error: ${error.message}`
        };
      }
    } else {
      // Try to reinitialize
      const initialized = await initializePrinter();
      if (initialized && printer) {
        const isConnected = await printer.isPrinterConnected();
        status = {
          connected: isConnected,
          type: isConnected ? 'thermal' : 'system',
          message: isConnected ? 'Thermal printer ready' : 'Using system default printer'
        };
      } else {
        status = {
          connected: false,
          type: 'simulated',
          message: 'No printer available - using simulation mode'
        };
      }
    }

    res.json(status);
  } catch (error) {
    res.status(500).json({
      connected: false,
      type: 'error',
      message: `Failed to check printer status: ${error.message}`
    });
  }
});

// Reinitialize printer
app.post('/api/printer/reinitialize', async (req, res) => {
  try {
    console.log('🔄 Manual printer reinitialization requested...');
    const success = await initializePrinter();

    if (success && printer) {
      const isConnected = await printer.isPrinterConnected();
      res.json({
        success: true,
        connected: isConnected,
        message: isConnected ? 'Printer reinitialized successfully' : 'Printer initialized but not responding'
      });
    } else {
      res.json({
        success: false,
        connected: false,
        message: 'Failed to initialize printer - using simulation mode'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      connected: false,
      message: `Reinitialization failed: ${error.message}`
    });
  }
});

// Print receipt with enhanced printer handling
app.post('/api/print/:id', async (req, res) => {
  try {
    const receipt = receipts.find(r => r.id === req.params.id);
    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    const { paperWidth = '80mm' } = req.body;
    const charWidth = paperWidth === '80mm' ? 48 : 32; // Characters per line

    // Try to reinitialize printer if it's null
    if (!printer) {
      console.log('🔄 Attempting to reinitialize printer...');
      await initializePrinter();
    }

    if (printer) {
      try {
        // Check if printer is still connected
        const isConnected = await printer.isPrinterConnected();
        if (!isConnected) {
          console.log('⚠️ Printer disconnected, attempting to reconnect...');
          await initializePrinter();
        }

        // Clear any previous print jobs
        printer.clear();

        // Set printer configuration for thermal printing
        printer.setCharacterSet('PC437_USA');
        printer.setTextSize(0, 0); // Normal size
        printer.alignCenter();

        // Business header
        printer.bold(true);
        printer.println(receipt.businessInfo.name);
        printer.bold(false);

        // Handle multi-line address
        const addressLines = receipt.businessInfo.address.split('\n');
        addressLines.forEach(line => {
          if (line.trim()) printer.println(line.trim());
        });

        printer.println(receipt.businessInfo.contact);
        printer.newLine();

        // Print separator line
        printer.println('-'.repeat(charWidth));

        // Items section
        printer.alignLeft();
        receipt.items.forEach(item => {
          const itemName = item.name.length > charWidth - 10 ?
            item.name.substring(0, charWidth - 13) + '...' : item.name;
          const itemTotal = `$${(item.price * item.quantity).toFixed(2)}`;
          const spaces = charWidth - itemName.length - itemTotal.length;

          printer.println(`${itemName}${' '.repeat(Math.max(1, spaces))}${itemTotal}`);
          printer.println(`  ${item.quantity} x $${item.price.toFixed(2)}`);
        });

        // Totals section
        printer.println('-'.repeat(charWidth));
        printer.alignRight();
        printer.println(`Subtotal: $${receipt.subtotal.toFixed(2)}`);
        printer.println(`Tax (8%): $${receipt.tax.toFixed(2)}`);
        printer.println('-'.repeat(charWidth));
        printer.bold(true);
        printer.println(`TOTAL: $${receipt.total.toFixed(2)}`);
        printer.bold(false);
        printer.println('-'.repeat(charWidth));

        // Payment info
        printer.alignLeft();
        printer.println(`Payment: ${receipt.paymentMethod}`);
        printer.println(`Ref: ${receipt.transactionRef}`);
        printer.println(`Date: ${new Date(receipt.timestamp).toLocaleString()}`);
        printer.newLine();

        // Footer text
        if (receipt.customization?.footerText) {
          printer.alignCenter();
          printer.println(receipt.customization.footerText);
          printer.newLine();
        }

        // Generate and print QR code
        const qrData = JSON.stringify({
          id: receipt.id,
          total: receipt.total,
          date: receipt.timestamp,
          business: receipt.businessInfo.name
        });

        try {
          const qrCode = await QRCode.toString(qrData, {
            type: 'terminal',
            width: paperWidth === '80mm' ? 25 : 20,
            margin: 1
          });
          printer.alignCenter();
          printer.println(qrCode);
        } catch (qrError) {
          console.log('⚠️ QR code generation failed:', qrError.message);
          // Continue without QR code
        }

        // Cut paper (if supported)
        printer.cut();

        // Execute print job
        console.log('🖨️ Sending print job to printer...');
        await printer.execute();

        console.log('✅ Receipt printed successfully');
        res.json({
          success: true,
          message: 'Receipt printed successfully',
          printerType: 'thermal'
        });

      } catch (printError) {
        console.error('❌ Print execution failed:', printError.message);

        // Try to reinitialize and retry once
        console.log('🔄 Retrying with printer reinitialization...');
        await initializePrinter();

        if (printer) {
          try {
            // Simplified retry
            printer.clear();
            printer.alignCenter();
            printer.bold(true);
            printer.println(receipt.businessInfo.name);
            printer.bold(false);
            printer.println(`Total: $${receipt.total.toFixed(2)}`);
            printer.println(`Ref: ${receipt.transactionRef}`);
            printer.cut();
            await printer.execute();

            res.json({
              success: true,
              message: 'Receipt printed successfully (simplified)',
              printerType: 'thermal'
            });
          } catch (retryError) {
            console.error('❌ Retry failed:', retryError.message);
            throw retryError;
          }
        } else {
          throw new Error('Printer reinitialization failed');
        }
      }
    } else {
      // Fallback: Use system print dialog or simulate printing
      console.log('🖨️ No thermal printer available, attempting system print...');

      // Try to use system default printer via different method
      try {
        // Create a simple text receipt for system printing
        const textReceipt = generateTextReceipt(receipt, charWidth);

        // Attempt to print to system default printer
        const systemPrintSuccess = await printToSystemPrinter(textReceipt, receipt.id);

        if (systemPrintSuccess) {
          console.log(`=== THERMAL RECEIPT ${paperWidth.toUpperCase()} (SYSTEM PRINT) ===`);
          console.log(textReceipt);
          console.log('===============================================');

          res.json({
            success: true,
            message: 'Receipt sent to system default printer successfully',
            printerType: 'system',
            receiptData: textReceipt
          });
        } else {
          throw new Error('System print command failed');
        }
      } catch (systemPrintError) {
        console.error('❌ System print failed:', systemPrintError.message);

        // Final fallback: simulation with detailed output
        const textReceipt = generateTextReceipt(receipt, charWidth);
        console.log(`=== THERMAL RECEIPT ${paperWidth.toUpperCase()} (SIMULATED) ===`);
        console.log(textReceipt);
        console.log('===============================================');

        res.json({
          success: true,
          message: 'Receipt printed successfully (simulated - no printer detected)',
          printerType: 'simulated',
          receiptData: textReceipt
        });
      }
    }
  } catch (error) {
    console.error('❌ Print receipt error:', error.message);
    res.status(500).json({
      error: 'Failed to print receipt',
      details: error.message,
      suggestion: 'Please check printer connection and try again'
    });
  }
});

// Helper function to generate text receipt
function generateTextReceipt(receipt, charWidth) {
  let text = '';

  // Header
  text += receipt.businessInfo.name + '\n';
  const addressLines = receipt.businessInfo.address.split('\n');
  addressLines.forEach(line => {
    if (line.trim()) text += line.trim() + '\n';
  });
  text += receipt.businessInfo.contact + '\n';
  text += '-'.repeat(charWidth) + '\n';

  // Items
  receipt.items.forEach(item => {
    const itemName = item.name.length > charWidth - 10 ?
      item.name.substring(0, charWidth - 13) + '...' : item.name;
    const itemTotal = `$${(item.price * item.quantity).toFixed(2)}`;
    const spaces = charWidth - itemName.length - itemTotal.length;

    text += `${itemName}${' '.repeat(Math.max(1, spaces))}${itemTotal}\n`;
    text += `  ${item.quantity} x $${item.price.toFixed(2)}\n`;
  });

  // Totals
  text += '-'.repeat(charWidth) + '\n';
  text += `${'Subtotal:'.padEnd(charWidth - 10)}$${receipt.subtotal.toFixed(2).padStart(8)}\n`;
  text += `${'Tax (8%):'.padEnd(charWidth - 10)}$${receipt.tax.toFixed(2).padStart(8)}\n`;
  text += '-'.repeat(charWidth) + '\n';
  text += `TOTAL: $${receipt.total.toFixed(2)}\n`;
  text += '-'.repeat(charWidth) + '\n';

  // Payment info
  text += `Payment: ${receipt.paymentMethod}\n`;
  text += `Ref: ${receipt.transactionRef}\n`;
  text += `Date: ${new Date(receipt.timestamp).toLocaleString()}\n`;

  // Footer
  if (receipt.customization?.footerText) {
    text += '\n' + receipt.customization.footerText + '\n';
  }

  return text;
}

// Function to print to system default printer
async function printToSystemPrinter(textContent, receiptId) {
  try {
    // Create a temporary text file
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFile = path.join(tempDir, `receipt-${receiptId}.txt`);
    fs.writeFileSync(tempFile, textContent, 'utf8');

    // Determine the OS and use appropriate print command
    const platform = process.platform;
    let printCommand;

    if (platform === 'win32') {
      // Windows: Use notepad to print to default printer
      printCommand = `notepad /p "${tempFile}"`;
    } else if (platform === 'darwin') {
      // macOS: Use lpr command
      printCommand = `lpr "${tempFile}"`;
    } else {
      // Linux: Use lpr command
      printCommand = `lpr "${tempFile}"`;
    }

    console.log(`🖨️ Executing print command: ${printCommand}`);

    // Execute the print command
    const { stdout, stderr } = await execAsync(printCommand);

    if (stderr && !stderr.includes('Warning')) {
      throw new Error(`Print command failed: ${stderr}`);
    }

    console.log('✅ System print command executed successfully');

    // Clean up temp file after a delay
    setTimeout(() => {
      try {
        if (fs.existsSync(tempFile)) {
          fs.unlinkSync(tempFile);
        }
      } catch (cleanupError) {
        console.log('⚠️ Failed to clean up temp file:', cleanupError.message);
      }
    }, 5000);

    return true;
  } catch (error) {
    console.error('❌ System print failed:', error.message);
    return false;
  }
}

// Generate PDF
app.post('/api/pdf/:id', async (req, res) => {
  try {
    const receipt = receipts.find(r => r.id === req.params.id);
    if (!receipt) {
      return res.status(404).json({ error: 'Receipt not found' });
    }

    const { paperWidth = '80mm' } = req.body;
    
    // Paper specifications for PDF generation
    const pdfSpecs = {
      '80mm': { width: 226, height: 600, margin: 10, fontSize: { normal: 10, small: 8, large: 12 } },
      '50mm': { width: 142, height: 600, margin: 8, fontSize: { normal: 8, small: 7, large: 10 } }
    };
    
    const specs = pdfSpecs[paperWidth];
    const doc = new PDFDocument({ size: [specs.width, specs.height], margin: specs.margin });
    const filename = `receipt-${receipt.id}.pdf`;
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    
    doc.pipe(res);
    
    // Business header
    doc.fontSize(specs.fontSize.large).text(receipt.businessInfo.name, { align: 'center' });
    doc.fontSize(specs.fontSize.small).text(receipt.businessInfo.address, { align: 'center' });
    doc.text(receipt.businessInfo.contact, { align: 'center' });
    doc.moveDown();
    
    // Dashed line separator
    const y1 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y1).lineTo(x + 2, y1).stroke();
    }
    doc.moveDown(0.5);
    
    // Items
    doc.fontSize(specs.fontSize.normal);
    receipt.items.forEach(item => {
      const itemText = `${item.name}`;
      const priceText = `$${(item.price * item.quantity).toFixed(2)}`;
      const qtyText = `${item.quantity} x $${item.price.toFixed(2)}`;
      
      doc.text(itemText, { continued: false });
      doc.fontSize(specs.fontSize.small).text(qtyText, { indent: 10 });
      doc.fontSize(specs.fontSize.normal);
      doc.text(priceText, doc.x, doc.y - 20, { align: 'right' });
    });
    
    doc.moveDown();
    
    // Dashed line separator
    const y2 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y2).lineTo(x + 2, y2).stroke();
    }
    doc.moveDown(0.5);
    
    doc.fontSize(specs.fontSize.normal).text(`Subtotal: $${receipt.subtotal.toFixed(2)}`, { align: 'right' });
    doc.text(`Tax: $${receipt.tax.toFixed(2)}`, { align: 'right' });
    doc.fontSize(specs.fontSize.large).text(`TOTAL: $${receipt.total.toFixed(2)}`, { align: 'right' });
    doc.fontSize(specs.fontSize.normal);
    doc.moveDown();
    
    // Dashed line separator
    const y3 = doc.y;
    for (let x = specs.margin; x < specs.width - specs.margin; x += 4) {
      doc.moveTo(x, y3).lineTo(x + 2, y3).stroke();
    }
    doc.moveDown(0.5);
    
    doc.fontSize(specs.fontSize.small).text(`Payment: ${receipt.paymentMethod}`);
    doc.text(`Ref: ${receipt.transactionRef}`);
    
    // QR Code
    const qrData = JSON.stringify({
      id: receipt.id,
      total: receipt.total,
      date: receipt.timestamp
    });
    const qrCode = await QRCode.toDataURL(qrData);
    const qrBuffer = Buffer.from(qrCode.split(',')[1], 'base64');
    const qrSize = paperWidth === '80mm' ? 80 : 60;
    doc.image(qrBuffer, { fit: [qrSize, qrSize], align: 'center' });
    
    doc.end();
  } catch (error) {
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});