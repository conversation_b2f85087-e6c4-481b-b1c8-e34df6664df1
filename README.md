# Thermal Receipt Generator

A full-stack application for generating and printing thermal receipts with QR codes.

## Features

- **Receipt Designer**: Interactive form-based receipt customization
- **Live Preview**: Real-time receipt preview with thermal printer formatting
- **QR Code Generation**: Dynamic QR codes with transaction details
- **Thermal Printing**: Backend integration with thermal printers
- **PDF Export**: Generate PDF receipts for digital storage
- **Receipt Templates**: Save and load receipt templates
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

### Frontend
- React.js with TypeScript
- Tailwind CSS for styling
- Lucide React for icons
- qrcode.react for QR code generation

### Backend
- Node.js with Express
- QR code generation with `qrcode`
- Thermal printer integration with `node-thermal-printer`
- PDF generation with `pdfkit`

## Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Backend Server**
   ```bash
   npm run server
   ```

3. **Start the Frontend Development Server**
   ```bash
   npm run dev
   ```

4. **Open in Browser**
   Navigate to `http://localhost:5173`

## Usage

### Creating a Receipt

1. **Business Information**: Fill in your business name, address, and contact details
2. **Add Items**: Use the "Add Item" button to add products/services with quantities and prices
3. **Payment Details**: Select payment method and enter transaction reference
4. **Customize**: Adjust font size and footer text
5. **Preview**: View the thermal receipt preview in real-time

### Printing

1. **Save Receipt**: Click "Save Receipt" to store the receipt
2. **Print**: Click "Print" to send to thermal printer (or simulate if no printer available)
3. **Export PDF**: Click "Export PDF" to download as PDF

### Thermal Printer Setup

The application supports thermal printers via the `node-thermal-printer` library. Configure your printer settings in `server.js`:

```javascript
const printer = new ThermalPrinter({
  type: 'epson',
  interface: 'printer:Auto', // or specific printer interface
  options: {
    timeout: 5000,
  },
});
```

## API Endpoints

- `POST /api/qr-code` - Generate QR code
- `POST /api/receipts` - Save receipt
- `GET /api/receipts` - Get all receipts
- `GET /api/receipts/:id` - Get receipt by ID
- `POST /api/print/:id` - Print receipt
- `POST /api/pdf/:id` - Generate PDF

## Customization

### Receipt Layout
- Font size adjustment (10-16px)
- Custom footer text
- Business logo support (planned)

### Thermal Printer Settings
- Paper width: 226px (standard thermal paper)
- Monospace font for consistent alignment
- QR code size: 80x80px

## Development

### Frontend Structure
```
src/
├── App.tsx          # Main application component
├── main.tsx         # React entry point
└── index.css        # Global styles
```

### Backend Structure
```
server.js            # Express server with all API endpoints
```

## Production Deployment

1. **Build Frontend**
   ```bash
   npm run build
   ```

2. **Configure Production Server**
   - Set up proper database (replace in-memory storage)
   - Configure thermal printer connections
   - Set up HTTPS for production

3. **Environment Variables**
   ```bash
   PORT=3001
   NODE_ENV=production
   ```

## Troubleshooting

### Thermal Printer Issues
- Ensure printer is properly connected
- Check printer interface settings
- Review printer driver installation

### QR Code Generation
- Verify QR code data format
- Check network connectivity for API calls
- Ensure proper JSON formatting

## License

MIT License - feel free to use and modify for your projects.